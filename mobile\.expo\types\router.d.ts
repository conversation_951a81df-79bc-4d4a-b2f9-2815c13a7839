/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/intro`; params?: Router.UnknownInputParams; } | { pathname: `/../services/facility/public-place`; params?: Router.UnknownInputParams; } | { pathname: `/../lib/forms/publicPlace`; params?: Router.UnknownInputParams; } | { pathname: `/../components/CreatePublicPlaceForm`; params?: Router.UnknownInputParams; } | { pathname: `/../components/data-collection/public-place/GeneralInformation`; params?: Router.UnknownInputParams; } | { pathname: `/../components/data-collection/public-place/Hygiene`; params?: Router.UnknownInputParams; } | { pathname: `/../components/data-collection/public-place/LiquidWaste`; params?: Router.UnknownInputParams; } | { pathname: `/../components/data-collection/public-place/Sanitation`; params?: Router.UnknownInputParams; } | { pathname: `/../components/data-collection/public-place/SolidWaste`; params?: Router.UnknownInputParams; } | { pathname: `/../components/data-collection/public-place/WaterSupply`; params?: Router.UnknownInputParams; } | { pathname: `/../components/data-collection/PublicPlaceForm`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `/auth/login`; params?: Router.UnknownInputParams; } | { pathname: `/auth/verify2FA`; params?: Router.UnknownInputParams; } | { pathname: `/facility/location`; params?: Router.UnknownInputParams; } | { pathname: `/facility/new`; params?: Router.UnknownInputParams; } | { pathname: `/facility/forms`; params?: Router.UnknownInputParams; } | { pathname: `/main`; params?: Router.UnknownInputParams; } | { pathname: `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/facility/[type]`, params: Router.UnknownInputParams & { type: string | number; } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/intro`; params?: Router.UnknownOutputParams; } | { pathname: `/../services/facility/public-place`; params?: Router.UnknownOutputParams; } | { pathname: `/../lib/forms/publicPlace`; params?: Router.UnknownOutputParams; } | { pathname: `/../components/CreatePublicPlaceForm`; params?: Router.UnknownOutputParams; } | { pathname: `/../components/data-collection/public-place/GeneralInformation`; params?: Router.UnknownOutputParams; } | { pathname: `/../components/data-collection/public-place/Hygiene`; params?: Router.UnknownOutputParams; } | { pathname: `/../components/data-collection/public-place/LiquidWaste`; params?: Router.UnknownOutputParams; } | { pathname: `/../components/data-collection/public-place/Sanitation`; params?: Router.UnknownOutputParams; } | { pathname: `/../components/data-collection/public-place/SolidWaste`; params?: Router.UnknownOutputParams; } | { pathname: `/../components/data-collection/public-place/WaterSupply`; params?: Router.UnknownOutputParams; } | { pathname: `/../components/data-collection/PublicPlaceForm`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/login`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/verify2FA`; params?: Router.UnknownOutputParams; } | { pathname: `/facility/location`; params?: Router.UnknownOutputParams; } | { pathname: `/facility/new`; params?: Router.UnknownOutputParams; } | { pathname: `/facility/forms`; params?: Router.UnknownOutputParams; } | { pathname: `/main`; params?: Router.UnknownOutputParams; } | { pathname: `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `/facility/[type]`, params: Router.UnknownOutputParams & { type: string; } };
      href: Router.RelativePathString | Router.ExternalPathString | `/${`?${string}` | `#${string}` | ''}` | `/intro${`?${string}` | `#${string}` | ''}` | `/../services/facility/public-place${`?${string}` | `#${string}` | ''}` | `/../lib/forms/publicPlace${`?${string}` | `#${string}` | ''}` | `/../components/CreatePublicPlaceForm${`?${string}` | `#${string}` | ''}` | `/../components/data-collection/public-place/GeneralInformation${`?${string}` | `#${string}` | ''}` | `/../components/data-collection/public-place/Hygiene${`?${string}` | `#${string}` | ''}` | `/../components/data-collection/public-place/LiquidWaste${`?${string}` | `#${string}` | ''}` | `/../components/data-collection/public-place/Sanitation${`?${string}` | `#${string}` | ''}` | `/../components/data-collection/public-place/SolidWaste${`?${string}` | `#${string}` | ''}` | `/../components/data-collection/public-place/WaterSupply${`?${string}` | `#${string}` | ''}` | `/../components/data-collection/PublicPlaceForm${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `/auth/login${`?${string}` | `#${string}` | ''}` | `/auth/verify2FA${`?${string}` | `#${string}` | ''}` | `/facility/location${`?${string}` | `#${string}` | ''}` | `/facility/new${`?${string}` | `#${string}` | ''}` | `/facility/forms${`?${string}` | `#${string}` | ''}` | `/main${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/intro`; params?: Router.UnknownInputParams; } | { pathname: `/../services/facility/public-place`; params?: Router.UnknownInputParams; } | { pathname: `/../lib/forms/publicPlace`; params?: Router.UnknownInputParams; } | { pathname: `/../components/CreatePublicPlaceForm`; params?: Router.UnknownInputParams; } | { pathname: `/../components/data-collection/public-place/GeneralInformation`; params?: Router.UnknownInputParams; } | { pathname: `/../components/data-collection/public-place/Hygiene`; params?: Router.UnknownInputParams; } | { pathname: `/../components/data-collection/public-place/LiquidWaste`; params?: Router.UnknownInputParams; } | { pathname: `/../components/data-collection/public-place/Sanitation`; params?: Router.UnknownInputParams; } | { pathname: `/../components/data-collection/public-place/SolidWaste`; params?: Router.UnknownInputParams; } | { pathname: `/../components/data-collection/public-place/WaterSupply`; params?: Router.UnknownInputParams; } | { pathname: `/../components/data-collection/PublicPlaceForm`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `/auth/login`; params?: Router.UnknownInputParams; } | { pathname: `/auth/verify2FA`; params?: Router.UnknownInputParams; } | { pathname: `/facility/location`; params?: Router.UnknownInputParams; } | { pathname: `/facility/new`; params?: Router.UnknownInputParams; } | { pathname: `/facility/forms`; params?: Router.UnknownInputParams; } | { pathname: `/main`; params?: Router.UnknownInputParams; } | { pathname: `/profile`; params?: Router.UnknownInputParams; } | `/facility/${Router.SingleRoutePart<T>}` | { pathname: `/facility/[type]`, params: Router.UnknownInputParams & { type: string | number; } };
    }
  }
}
