import AppText from '@/components/ui/Text';
import * as enums from '@/types/enums';
import { getEnumOptions } from '@/utils/enum';
import { Picker } from '@react-native-picker/picker';
import React from 'react';
import { Controller } from 'react-hook-form';
import { View } from 'react-native';
import { TextInput } from 'react-native-paper';
import tw from 'twrnc';

const GeneralInfoSection = ({ control, errors }: any) => {
    return (
        <>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Health Facility Name</AppText>
                <Controller
                    control={control}
                    name="generalInfo.facilityName"
                    render={({ field: { onChange, onBlur, value } }) => (
                        <TextInput
                            style={tw`bg-white`}
                            mode="outlined"
                            placeholder="Health Facility Name"
                            outlineColor="#E5E7EB"
                            value={value}
                            onChangeText={onChange}
                            onBlur={onBlur}
                        />
                    )}
                />
                {errors.generalInfo?.facilityName && <AppText style={tw`text-red-500`}>{errors.generalInfo?.facilityName.message}</AppText>}
            </View>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Type of Health Facility</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="generalInfo.facilityType"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value} onValueChange={onChange}>
                                <Picker.Item label="Select Facility Type" value="" />
                                {getEnumOptions(enums.HealthFacilityType).map(opt => (
                                    <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                ))}
                            </Picker>
                        )}
                    />
                </View>
                {errors.generalInfo?.facilityType && <AppText style={tw`text-red-500`}>{errors.generalInfo?.facilityType.message}</AppText>}
            </View>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Health Facility Management</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="generalInfo.managementType"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value} onValueChange={onChange}>
                                <Picker.Item label="Select Management Type" value="" />
                                {getEnumOptions(enums.HealthFacilityManagement).map(opt => (
                                    <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                ))}
                            </Picker>
                        )}
                    />
                </View>
                {errors.generalInfo?.managementType && <AppText style={tw`text-red-500`}>{errors.generalInfo?.managementType.message}</AppText>}
            </View>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Daily Patient Volume</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="generalInfo.dailyPatientVolume"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value} onValueChange={onChange}>
                                <Picker.Item label="Select Daily Patient Volume" value="" />
                                {getEnumOptions(enums.DailyPatientVolume).map(opt => (
                                    <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                ))}
                            </Picker>
                        )}
                    />
                </View>
                {errors.generalInfo?.dailyPatientVolume && <AppText style={tw`text-red-500`}>{errors.generalInfo?.dailyPatientVolume.message}</AppText>}
            </View>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Total Number of Staff</AppText>
                <Controller
                    control={control}
                    name="generalInfo.totalStaff"
                    render={({ field: { onChange, onBlur, value } }) => (
                        <TextInput
                            style={tw`bg-white`}
                            mode="outlined"
                            placeholder="Total Staff"
                            outlineColor="#E5E7EB"
                            inputMode="numeric"
                            value={value}
                            onChangeText={onChange}
                            onBlur={onBlur}
                        />
                    )}
                />
                {errors.generalInfo?.totalStaff && <AppText style={tw`text-red-500`}>{errors.generalInfo?.totalStaff.message}</AppText>}
            </View>
        </>
    );
}

export default GeneralInfoSection;