import HealthFacilityForm from "@/components/data-collection/HealthFacilityForm";
import HouseHoldForm from "@/components/data-collection/HouseHoldForm";
import PublicPlaceForm from "@/components/data-collection/PublicPlaceForm";
import SchoolForm from "@/components/data-collection/SchoolForm";
import AppText from "@/components/ui/Text";
import { getHealthFacility, submitHealthFacilityForm } from "@/services/facility/health";
import { getHouseHold, submitHouseholdForm } from "@/services/facility/household";
import { submitPublicPlaceForm } from "@/services/facility/public-place";
import { getSchool, submitSchoolForm } from "@/services/facility/school";
import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import { Alert, View } from "react-native";
import tw from "twrnc";

export default function FormsScreen() {

    const { type, facilityId } = useLocalSearchParams();
    const displayType = Array.isArray(type) ? type[0] : type;
    const _type = displayType.toLocaleLowerCase().split(" ").join("");
    const [loading, setLoading] = useState(false);
    const [facility, setFacility] = useState<any>(null);

    useEffect(() => {
        if (!facilityId) {
            Alert.alert("Error", "Failed to load facility");
            router.replace('/main');
        }

        const loadFacility = async () => {
            setLoading(true);
            try {
                switch (_type) {
                    case "household":
                        const res = await getHouseHold(facilityId.toString());
                        setFacility(res.data);
                        break;
                    case "school":
                        const res1 = await getSchool(facilityId.toString());
                        setFacility(res1.data);
                        break;
                    case "healthfacility":
                        const res2 = await getHealthFacility(facilityId.toString());
                        setFacility(res2.data);
                        break;
                    default:
                        break;
                }
            } catch (error) {
                Alert.alert("Error", "Failed to load facility");
            } finally {
                setLoading(false);
            }
        };

        loadFacility();

    }, [facilityId]);

    const submitHouseHoldInfo = async (data: any) => {
        setLoading(true);
        try {
            await submitHouseholdForm(facilityId.toString(), data);
            Alert.alert("Success", "Household data submitted successfully", [
                { text: 'OK', onPress: () => router.replace('/main') },
            ]);
        } catch (error: any) {
            console.log(error.response.data.message);
            Alert.alert("Something went wrong", "Failed to submit household WASH information.");
        } finally {
            setLoading(false);
        }
    };

    const submitSchoolInfo = async (data: any) => {
        setLoading(true);
        try {
            await submitSchoolForm(facilityId.toString(), data);
            Alert.alert("Success", "School data submitted successfully", [
                { text: 'OK', onPress: () => router.replace('/main') },
            ]);
        } catch (error: any) {
            console.log(error.response);
            Alert.alert("Something went wrong", "Failed to submit school WASH information.");
        } finally {
            setLoading(false);
        }
    };

    const submitHealthFacilityInfo = async (data: any) => {
        setLoading(true);

        try {
            await submitHealthFacilityForm(facilityId.toString(), data);
            Alert.alert("Success", "Health facility data submitted successfully", [
                { text: 'OK', onPress: () => router.replace('/main') },
            ]);
        } catch (error: any) {
            console.log(error.response.data.message);
            Alert.alert("Something went wrong", "Failed to submit health facility WASH information.");
        } finally {
            setLoading(false);
        }
    }
    
    const submitPublicPlaceInfo = async (data: any) => {
        setLoading(true);
        try {
            await submitPublicPlaceForm(facilityId.toString(), data);
            Alert.alert("Success", "Public place data submitted successfully", [
                { text: 'OK', onPress: () => router.replace('/main') },
            ]);
        } catch (error: any) {
            console.log(error.response.data.message);
            Alert.alert("Something went wrong", "Failed to submit public place WASH information.");
        } finally {
            setLoading(false);
        }
    }

    if (_type === "household") {
        return (
            <View style={tw`flex-1 bg-white px-6 py-8`}>
                <HouseHoldForm data={facility} onSubmit={submitHouseHoldInfo} />
            </View>
        );
    } else if (_type === "market") {
        return (
            <View style={tw`flex-1 bg-white px-6 py-8`}>
                <AppText>Market Form</AppText>
            </View>
        );
    } else if (_type === "school") {
        return (
            <View style={tw`flex-1 bg-white px-6 py-8`}>
                <SchoolForm data={facility} onSubmit={submitSchoolInfo} />
            </View>
        );
    } else if (_type === "healthfacility") {
        return (
            <View style={tw`flex-1 bg-white px-6 py-8`}>
                <HealthFacilityForm data={facility} onSubmit={submitHealthFacilityInfo} />
            </View>
        );

    } else if (_type === "publicplace") {
        return (
            <View style={tw`flex-1 bg-white px-6 py-8`}>
                <PublicPlaceForm data={facility} onSubmit={submitPublicPlaceInfo} />
            </View>
        );
    } else {
        return (
            <View style={tw`flex-1 bg-white px-6 py-8`}>
                <AppText>Other Form</AppText>
            </View>
        );
    }
}