import AppText from '@/components/ui/Text';
import * as enums from '@/types/enums';
import { getEnumOptions, isEnumKey } from '@/utils/enum';
import { Picker } from '@react-native-picker/picker';
import React from 'react';
import { Controller, useWatch } from 'react-hook-form';
import { View } from 'react-native';
import { TextInput } from 'react-native-paper';
import tw from 'twrnc';

const SanitationSection = ({ control, errors }: any) => {
    const hasToiletFullInLast2Years = useWatch({ control, name: 'sanitation.hasToiletFullInLast2Years' });
    const toiletType = useWatch({ control, name: 'sanitation.toiletType' });
    return (
        <>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Toilet facility mainly used at the public place</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="sanitation.toiletType"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value} onValueChange={onChange}>
                                <Picker.Item label="Select Toilet Type" value="" />
                                {getEnumOptions(enums.ToiletFacilityType).map(opt => (
                                    <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                ))}
                            </Picker>
                        )}
                    />
                </View>
                {errors.sanitation?.toiletType && <AppText style={tw`text-red-500`}>{errors.sanitation?.toiletType.message}</AppText>}
            </View>
            {(isEnumKey(enums.ToiletFacilityType, 'PIT_WITH_SLAB', toiletType)
                || isEnumKey(enums.ToiletFacilityType, 'PIT_WITHOUT_SLAB', toiletType)
                || isEnumKey(enums.ToiletFacilityType, 'VENTILATED_IMPROVED_PIT', toiletType)
                || isEnumKey(enums.ToiletFacilityType, 'COMPOSTING_TOILET', toiletType)
                || isEnumKey(enums.ToiletFacilityType, 'URINE_DIVERSION_DRY_TOILET', toiletType))
                && <View style={tw`mb-4`}>
                    <AppText style={tw`mb-2`}>Toilet facility slab construction materials (super structure)</AppText>
                    <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                        <Controller
                            control={control}
                            name="sanitation.slabConstructionMaterial"
                            render={({ field: { onChange, value } }) => (
                                <Picker selectedValue={value} onValueChange={onChange}>
                                    <Picker.Item label="Select Material" value="" />
                                    {getEnumOptions(enums.FacilitySlabConstructionMaterial).map(opt => (
                                        <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                    ))}
                                </Picker>
                            )}
                        />
                    </View>
                    {errors.sanitation?.slabConstructionMaterial && <AppText style={tw`text-red-500`}>{errors.sanitation?.slabConstructionMaterial.message}</AppText>}
                </View>}
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Number of toilet facilities available at the public place</AppText>
                <Controller
                    control={control}
                    name="sanitation.totalToilets"
                    render={({ field: { onChange, onBlur, value } }) => (
                        <TextInput
                            style={tw`bg-white`}
                            mode="outlined"
                            placeholder="Total Toilets"
                            outlineColor="#E5E7EB"
                            inputMode="numeric"
                            value={value}
                            onChangeText={onChange}
                            onBlur={onBlur}
                        />
                    )}
                />
                {errors.sanitation?.totalToilets && <AppText style={tw`text-red-500`}>{errors.sanitation?.totalToilets.message}</AppText>}
            </View>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Are male and female toilets separated?</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="sanitation.genderSeparation"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value === true ? 'yes' : value === false ? 'no' : ''} onValueChange={v => onChange(v === 'yes')}>
                                <Picker.Item label="Select Option" value="" />
                                <Picker.Item label="Yes" value="yes" />
                                <Picker.Item label="No" value="no" />
                            </Picker>
                        )}
                    />
                </View>
                {errors.sanitation?.genderSeparation && <AppText style={tw`text-red-500`}>{errors.sanitation?.genderSeparation.message}</AppText>}
            </View>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Number of toilets used by females solely</AppText>
                <Controller
                    control={control}
                    name="sanitation.femaleToilets"
                    render={({ field: { onChange, onBlur, value } }) => (
                        <TextInput
                            style={tw`bg-white`}
                            mode="outlined"
                            placeholder="Female Toilets"
                            outlineColor="#E5E7EB"
                            inputMode="numeric"
                            value={value}
                            onChangeText={onChange}
                            onBlur={onBlur}
                        />
                    )}
                />
                {errors.sanitation?.femaleToilets && <AppText style={tw`text-red-500`}>{errors.sanitation?.femaleToilets.message}</AppText>}
            </View>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Number of toilets used by males solely</AppText>
                <Controller
                    control={control}
                    name="sanitation.maleToilets"
                    render={({ field: { onChange, onBlur, value } }) => (
                        <TextInput
                            style={tw`bg-white`}
                            mode="outlined"
                            placeholder="Male Toilets"
                            outlineColor="#E5E7EB"
                            inputMode="numeric"
                            value={value}
                            onChangeText={onChange}
                            onBlur={onBlur}
                        />
                    )}
                />
                {errors.sanitation?.maleToilets && <AppText style={tw`text-red-500`}>{errors.sanitation?.maleToilets.message}</AppText>}
            </View>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Does the public place have toilets accessible to people with disabilities?</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="sanitation.disabilityAccess"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value === true ? 'yes' : value === false ? 'no' : ''} onValueChange={v => onChange(v === 'yes')}>
                                <Picker.Item label="Select Option" value="" />
                                <Picker.Item label="Yes" value="yes" />
                                <Picker.Item label="No" value="no" />
                            </Picker>
                        )}
                    />
                </View>
                {errors.sanitation?.disabilityAccess && <AppText style={tw`text-red-500`}>{errors.sanitation?.disabilityAccess.message}</AppText>}
            </View>

            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Has any toilet facility been full of excreta in the previous 2 years?</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="sanitation.hasToiletFullInLast2Years"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value === true ? 'yes' : value === false ? 'no' : ''} onValueChange={v => onChange(v === 'yes')}>
                                <Picker.Item label="Select Option" value="" />
                                <Picker.Item label="Yes" value="yes" />
                                <Picker.Item label="No" value="no" />
                            </Picker>
                        )}
                    />
                </View>
                {errors.sanitation?.hasToiletFullInLast2Years && <AppText style={tw`text-red-500`}>{errors.sanitation?.hasToiletFullInLast2Years.message}</AppText>}
            </View>
            {hasToiletFullInLast2Years && (
                <View style={tw`mb-4`}>
                    <AppText style={tw`mb-2`}>How was the excreta managed when the toilet got full?</AppText>
                    <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                        <Controller
                            control={control}
                            name="sanitation.excretaManagement"
                            render={({ field: { onChange, value } }) => (
                                <Picker selectedValue={value} onValueChange={onChange}>
                                    <Picker.Item label="Select Management" value="" />
                                    {getEnumOptions(enums.ExcretaManagement).map(opt => (
                                        <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                    ))}
                                </Picker>
                            )}
                        />
                    </View>
                    {errors.sanitation?.excretaManagement && <AppText style={tw`text-red-500`}>{errors.sanitation?.excretaManagement.message}</AppText>}
                </View>
            )}
        </>
    );
};

export default SanitationSection;