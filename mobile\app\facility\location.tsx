import AppButton from "@/components/ui/Button";
import AppText from "@/components/ui/Text";
import { PRIMARY_COLOR } from "@/constants/colors";
import {
    getCells,
    getDistricts,
    getProvinces,
    getSectors,
    getVillages,
} from "@/services/administrative";
import { Cell, District, Province, Sector, Village } from "@/types/administrative";
import { Picker } from "@react-native-picker/picker";
import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import { Alert, View } from "react-native";
import tw from "twrnc";

export default function LocationScreen() {
    const [provinces, setProvinces] = useState<Province[]>([]);
    const [districts, setDistricts] = useState<District[]>([]);
    const [sectors, setSectors] = useState<Sector[]>([]);
    const [cells, setCells] = useState<Cell[]>([]);
    const [villages, setVillages] = useState<Village[]>([]);

    const [selectedProvince, setSelectedProvince] = useState<number | undefined>();
    const [selectedDistrict, setSelectedDistrict] = useState<number | undefined>();
    const [selectedSector, setSelectedSector] = useState<number | undefined>();
    const [selectedCell, setSelectedCell] = useState<number | undefined>();
    const [selectedVillage, setSelectedVillage] = useState<number | undefined>();

    const { type } = useLocalSearchParams();

    const [loading, setLoading] = useState({
        provinces: false,
        districts: false,
        sectors: false,
        cells: false,
        villages: false,
    });

    // Load provinces on mount
    useEffect(() => {
        const loadProvinces = async () => {
            setLoading((prev) => ({ ...prev, provinces: true }));
            try {
                const res = await getProvinces();
                setProvinces(res.data.provinces);
            } catch (error) {
                Alert.alert("Error", "Failed to load provinces");
            } finally {
                setLoading((prev) => ({ ...prev, provinces: false }));
            }
        };
        loadProvinces();
    }, []);

    // Load districts when province changes
    useEffect(() => {
        if (!selectedProvince) {
            setDistricts([]);
            setSelectedDistrict(undefined);
            setSectors([]);
            setSelectedSector(undefined);
            setCells([]);
            setSelectedCell(undefined);
            setVillages([]);
            setSelectedVillage(undefined);
            return;
        }
        const loadDistricts = async () => {
            setLoading((prev) => ({ ...prev, districts: true }));
            try {
                const res = await getDistricts(selectedProvince);
                setDistricts(res.data.districts);
            } catch (error) {
                Alert.alert("Error", "Failed to load districts");
            } finally {
                setLoading((prev) => ({ ...prev, districts: false }));
            }
        };
        loadDistricts();
        setSelectedDistrict(undefined);
        setSectors([]);
        setSelectedSector(undefined);
        setCells([]);
        setSelectedCell(undefined);
        setVillages([]);
        setSelectedVillage(undefined);
    }, [selectedProvince]);

    // Load sectors when district changes
    useEffect(() => {
        if (!selectedDistrict) {
            setSectors([]);
            setSelectedSector(undefined);
            setCells([]);
            setSelectedCell(undefined);
            setVillages([]);
            setSelectedVillage(undefined);
            return;
        }
        const loadSectors = async () => {
            setLoading((prev) => ({ ...prev, sectors: true }));
            try {
                const res = await getSectors(selectedDistrict);
                setSectors(res.data.sectors);
            } catch (error) {
                Alert.alert("Error", "Failed to load sectors");
            } finally {
                setLoading((prev) => ({ ...prev, sectors: false }));
            }
        };
        loadSectors();
        setSelectedSector(undefined);
        setCells([]);
        setSelectedCell(undefined);
        setVillages([]);
        setSelectedVillage(undefined);
    }, [selectedDistrict]);

    // Load cells when sector changes
    useEffect(() => {
        if (!selectedSector) {
            setCells([]);
            setSelectedCell(undefined);
            setVillages([]);
            setSelectedVillage(undefined);
            return;
        }
        const loadCells = async () => {
            setLoading((prev) => ({ ...prev, cells: true }));
            try {
                const res = await getCells(selectedSector);
                setCells(res.data.cells);
            } catch (error) {
                Alert.alert("Error", "Failed to load cells");
            } finally {
                setLoading((prev) => ({ ...prev, cells: false }));
            }
        };
        loadCells();
        setSelectedCell(undefined);
        setVillages([]);
        setSelectedVillage(undefined);
    }, [selectedSector]);

    // Load villages when cell changes
    useEffect(() => {
        if (!selectedCell) {
            setVillages([]);
            setSelectedVillage(undefined);
            return;
        }
        const loadVillages = async () => {
            setLoading((prev) => ({ ...prev, villages: true }));
            try {
                const res = await getVillages(selectedCell);
                setVillages(res.data.villages);
            } catch (error) {
                Alert.alert("Error", "Failed to load villages");
            } finally {
                setLoading((prev) => ({ ...prev, villages: false }));
            }
        };
        loadVillages();
        setSelectedVillage(undefined);
    }, [selectedCell]);

    return (
        <View style={tw`flex-1 bg-white pt-2 px-6`}>
            <View>
                <AppText
                    weight="bold"
                    style={tw`text-2xl mb-2 text-[${PRIMARY_COLOR}]`}
                >
                    Select Location
                </AppText>
                <AppText weight="medium" style={tw`text-gray-600`}>
                    choose the location you are going to collect the data for
                </AppText>
            </View>

            {/* Province Picker */}
            <View style={tw`mt-6 mb-3`}>
                <AppText style={tw`mb-2 text-base font-medium`}>Province</AppText>
                <View
                    style={tw`${loading.provinces || provinces.length === 0 ? "bg-gray-100" : "bg-white"} border border-gray-300 rounded`}
                >
                    <Picker
                        selectedValue={selectedProvince?.toString() || ""}
                        onValueChange={(val) => setSelectedProvince(val ? Number(val) : undefined)}
                        enabled={!loading.provinces && provinces.length > 0}
                    >
                        <Picker.Item label="Select Province" value="" />
                        {provinces.map((p) => (
                            <Picker.Item key={p.id} label={p.name || "Unknown"} value={p.id?.toString() || ""} />
                        ))}
                    </Picker>
                </View>
            </View>

            {/* District Picker */}
            <View style={tw`mb-3`}>
                <AppText style={tw`mb-2 text-base font-medium`}>District</AppText>
                <View
                    style={tw`${loading.districts || !selectedProvince || districts.length === 0 ? "bg-gray-100" : "bg-white"} border border-gray-300 rounded`}
                >
                    <Picker
                        selectedValue={selectedDistrict?.toString() || ""}
                        onValueChange={(val) => setSelectedDistrict(val ? Number(val) : undefined)}
                        enabled={!loading.districts && !!selectedProvince && districts.length > 0}
                    >
                        <Picker.Item label="Select District" value="" />
                        {districts.map((d) => (
                            <Picker.Item key={d.id} label={d.name || "Unknown"} value={d.id?.toString() || ""} />
                        ))}
                    </Picker>
                </View>
            </View>

            {/* Sector Picker */}
            <View style={tw`mb-3`}>
                <AppText style={tw`mb-2 text-base font-medium`}>Sector</AppText>
                <View
                    style={tw`${loading.sectors || !selectedDistrict || sectors.length === 0 ? "bg-gray-100" : "bg-white"} border border-gray-300 rounded`}
                >
                    <Picker
                        selectedValue={selectedSector?.toString() || ""}
                        onValueChange={(val) => setSelectedSector(val ? Number(val) : undefined)}
                        enabled={!loading.sectors && !!selectedDistrict && sectors.length > 0}
                    >
                        <Picker.Item label="Select Sector" value="" />
                        {sectors.map((s) => (
                            <Picker.Item key={s.id} label={s.name || "Unknown"} value={s.id?.toString() || ""} />
                        ))}
                    </Picker>
                </View>
            </View>

            {/* Cell Picker */}
            <View style={tw`mb-3`}>
                <AppText style={tw`mb-2 text-base font-medium`}>Cell</AppText>
                <View
                    style={tw`${loading.cells || !selectedSector || cells.length === 0 ? "bg-gray-100" : "bg-white"} border border-gray-300 rounded`}
                >
                    <Picker
                        selectedValue={selectedCell?.toString() || ""}
                        onValueChange={(val) => setSelectedCell(val ? Number(val) : undefined)}
                        enabled={!loading.cells && !!selectedSector && cells.length > 0}
                    >
                        <Picker.Item label="Select Cell" value="" />
                        {cells.map((c) => (
                            <Picker.Item key={c.id} label={c.name || "Unknown"} value={c.id?.toString() || ""} />
                        ))}
                    </Picker>
                </View>
            </View>

            {/* Village Picker */}
            <View style={tw`mb-6`}>
                <AppText style={tw`mb-2 text-base font-medium`}>Village</AppText>
                <View
                    style={tw`${loading.villages || !selectedCell || villages.length === 0 ? "bg-gray-100" : "bg-white"} border border-gray-300 rounded`}
                >
                    <Picker
                        selectedValue={selectedVillage?.toString() || ""}
                        onValueChange={(val) => setSelectedVillage(val ? Number(val) : undefined)}
                        enabled={!loading.villages && !!selectedCell && villages.length > 0}
                    >
                        <Picker.Item label="Select Village" value="" />
                        {villages.map((v) => (
                            <Picker.Item key={v.id} label={v.name || "Unknown"} value={v.id?.toString() || ""} />
                        ))}
                    </Picker>
                </View>
            </View>

            <AppButton
                title="Continue"
                disabled={!selectedVillage}
                onPress={() => {
                    router.push({
                        pathname: "/facility/[type]",
                        params: { type: type.toString(), villageId: selectedVillage },
                    });
                }}
            />
        </View>
    );
}