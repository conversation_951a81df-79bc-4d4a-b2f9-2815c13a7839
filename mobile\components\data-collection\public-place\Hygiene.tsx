import AppText from '@/components/ui/Text';
import * as enums from '@/types/enums';
import { getEnumOptions, isEnumKey } from '@/utils/enum';
import { Picker } from '@react-native-picker/picker';
import React from 'react';
import { Controller, useWatch } from 'react-hook-form';
import { View } from 'react-native';
import tw from 'twrnc';

const HygieneSection = ({ control, errors }: any) => {
    const toiletType = useWatch({ control, name: 'sanitation.toiletType' });
    const handwashingFacility = useWatch({ control, name: 'hygiene.handwashingFacility' });
    const handWashingfacilityNearToilet = useWatch({ control, name: 'hygiene.handWashingfacilityNearToilet' });

    return (
        <>
            {!isEnumKey(enums.ToiletFacilityType, 'NO_FACILITY', toiletType) && (
                <View style={tw`mb-4`}>
                    <AppText style={tw`mb-2`}>Is there a handwashing facility available for use by those coming to the public place?</AppText>
                    <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                        <Controller
                            control={control}
                            name="hygiene.handwashingFacility"
                            render={({ field: { onChange, value } }) => (
                                <Picker selectedValue={value ? 'yes' : 'no'} onValueChange={v => onChange(v === 'yes')}>
                                    <Picker.Item label="Select Option" value="" />
                                    <Picker.Item label="Yes" value="yes" />
                                    <Picker.Item label="No" value="no" />
                                </Picker>
                            )}
                        />
                    </View>
                    {errors.hygiene?.handwashingFacility && <AppText style={tw`text-red-500`}>{errors.hygiene?.handwashingFacility.message}</AppText>}
                </View>
            )}

            {handwashingFacility && (
                <View style={tw`mb-4`}>
                    <AppText style={tw`mb-2`}>Handwashing Facility Type</AppText>
                    <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                        <Controller
                            control={control}
                            name="hygiene.facilityType"
                            render={({ field: { onChange, value } }) => (
                                <Picker selectedValue={value} onValueChange={onChange}>
                                    <Picker.Item label="Select Type" value="" />
                                    {getEnumOptions(enums.HandWashingFacilityType).map(opt => (
                                        <Picker.Item key={opt.value} label={opt.label} value={opt.value as string} />
                                    ))}
                                </Picker>
                            )}
                        />
                    </View>
                    {errors.hygiene?.facilityType && <AppText style={tw`text-red-500`}>{errors.hygiene?.facilityType.message}</AppText>}
                </View>
            )}

            {handwashingFacility && (
                <View style={tw`mb-4`}>
                    <AppText style={tw`mb-2`}>Handwashing Materials</AppText>
                    <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                        <Controller
                            control={control}
                            name="hygiene.handwashingMaterials"
                            render={({ field: { onChange, value } }) => (
                                <Picker selectedValue={value} onValueChange={onChange}>
                                    <Picker.Item label="Select Material" value="" />
                                    {getEnumOptions(enums.HandWashingMaterial).map(opt => (
                                        <Picker.Item key={opt.value} label={opt.label} value={opt.value as string} />
                                    ))}
                                </Picker>
                            )}
                        />
                    </View>
                    {errors.hygiene?.handwashingMaterials && <AppText style={tw`text-red-500`}>{errors.hygiene?.handwashingMaterials.message}</AppText>}
                </View>
            )}

            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Is there any handwashing facility nearby the toilet(s) within at least 5 meters?</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="hygiene.handWashingfacilityNearToilet"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value === true ? 'yes' : value === false ? 'no' : ''} onValueChange={v => onChange(v === 'yes')}>
                                <Picker.Item label="Select Option" value="" />
                                <Picker.Item label="Yes" value="yes" />
                                <Picker.Item label="No" value="no" />
                            </Picker>
                        )}
                    />
                </View>
                {errors.hygiene?.handWashingfacilityNearToilet && <AppText style={tw`text-red-500`}>{errors.hygiene?.handWashingfacilityNearToilet.message}</AppText>}
            </View>

            {handWashingfacilityNearToilet && (
                <>
                    <View style={tw`mb-4`}>
                        <AppText style={tw`mb-2`}>Handwashing Facility Type Near Toilet</AppText>
                        <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                            <Controller
                                control={control}
                                name="hygiene.toiletHandWashingFacilityType"
                                render={({ field: { onChange, value } }) => (
                                    <Picker selectedValue={value} onValueChange={onChange}>
                                        <Picker.Item label="Select Type" value="" />
                                        {getEnumOptions(enums.HandWashingFacilityType).map(opt => (
                                            <Picker.Item key={opt.value} label={opt.label} value={opt.value as string} />
                                        ))}
                                    </Picker>
                                )}
                            />
                        </View>
                        {errors.hygiene?.toiletHandWashingFacilityType && <AppText style={tw`text-red-500`}>{errors.hygiene?.toiletHandWashingFacilityType.message}</AppText>}
                    </View>
                    <View style={tw`mb-4`}>
                        <AppText style={tw`mb-2`}>Handwashing Materials Near Toilet</AppText>
                        <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                            <Controller
                                control={control}
                                name="hygiene.toiletHandwashingMaterials"
                                render={({ field: { onChange, value } }) => (
                                    <Picker selectedValue={value} onValueChange={onChange}>
                                        <Picker.Item label="Select Material" value="" />
                                        {getEnumOptions(enums.HandWashingMaterial).map(opt => (
                                            <Picker.Item key={opt.value} label={opt.label} value={opt.value as string} />
                                        ))}
                                    </Picker>
                                )}
                            />
                        </View>
                        {errors.hygiene?.toiletHandwashingMaterials && <AppText style={tw`text-red-500`}>{errors.hygiene?.toiletHandwashingMaterials.message}</AppText>}
                    </View>
                </>
            )}
        </>
    );
}

export default HygieneSection;